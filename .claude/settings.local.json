{"permissions": {"allow": ["<PERSON><PERSON>(mvn clean:*)", "Bash(./mvnw clean compile)", "<PERSON><PERSON>(mvn test:*)", "Bash(where java)", "Bash(where mvn)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(dir:*)", "Bash(java:*)", "<PERSON><PERSON>(mvn checkstyle:*)", "Bash(.mvnw.cmd checkstyle:check)", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__check_onboarding_performed", "mcp__serena__activate_project", "mcp__serena__search_for_pattern", "mcp__serena__find_symbol", "mcp__serena__find_file", "mcp__serena__read_file", "mcp__serena__list_dir", "mcp__serena__onboarding", "mcp__serena__get_symbols_overview", "mcp__serena__write_memory"], "deny": [], "defaultMode": "acceptEdits"}}